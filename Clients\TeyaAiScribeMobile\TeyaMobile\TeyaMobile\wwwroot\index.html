<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <title>TeyaMobile</title>
    <base href="/" />
    <link rel="stylesheet" href="_content/TeyaMobile.Shared/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="_content/TeyaMobile.Shared/app.css" />
    <link rel="stylesheet" href="app.css" />
    <link rel="icon" href="data:,">
    <link href="_content/Syncfusion.Blazor.Themes/material.css" rel="stylesheet" />
</head>

<body>

    <div class="status-bar-safe-area"></div>

    <div id="app">Loading...</div>

    

    <script>
        window.AppSettings = {
            uploadUrl: "@Configuration["EncounterNotesURLUpload"]"
            };
    </script>
    <script src="_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>
    <script src="_content/TeyaMobile.Shared/js/AudioRecorder.js"></script>
    <!--<script src="js/AudioRecorder.js" type="text/javascript"></script>-->
    <script src="_framework/blazor.webview.js" autostart="false"></script>

</body>
</html>