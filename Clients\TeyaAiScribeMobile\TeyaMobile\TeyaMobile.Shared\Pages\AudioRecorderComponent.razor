﻿@page "/recorder/{PatientId:guid}"
@page "/audio-recorder"
@using Microsoft.AspNetCore.Components
@using Microsoft.JSInterop
@using TeyaMobileModel.Model
@using TeyaMobileViewModel.ViewModel
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject ISpeechService SpeechService
@inject TeyaMobileViewModel.ViewModel.IAuthenticationService AuthService
@implements IDisposable

<div class="audio-recorder-container">
    <div class="status-header">
        <div class="status-indicator @GetStatusClass()">
            <div class="status-icon">
                @if (IsRecording && !IsPaused)
                {
                    <i class="fas fa-circle recording-dot"></i>
                }
                else if (IsPaused)
                {
                    <i class="fas fa-pause"></i>
                }
                else
                {
                    <i class="fas fa-microphone"></i>
                }
            </div>
            <span class="status-text">@GetStatusText()</span>
        </div>
    </div>

   <div class="animation-container">
        @if (IsWeb)
        {
            <div class="wave-container @(IsRecording && !IsPaused ? "active" : "")">
                <img src="_content/TeyaMobile.Shared/aiwave.gif" class="wave-gif @(IsRecording && !IsPaused ? "playing" : "")" alt="AI Wave Animation" />
                <div class="wave-overlay">
                    @for (int i = 0; i < 20; i++)
                    {
                        <div class="wave-bar" style="@GetWaveBarStyle(i)"></div>
                    }
                </div>
            </div>
        }
        else
        {
            <div class="native-animation-container">
                <img src="_content/TeyaMobile.Shared/images/aiwave.gif" class="wave-gif @(IsRecording && !IsPaused ? "playing" : "")" alt="AI Wave Animation" />
                <div class="pulse-overlay @(IsRecording && !IsPaused ? "active" : "")">
                    <div class="pulse-ring pulse-ring-1"></div>
                    <div class="pulse-ring pulse-ring-2"></div>
                    <div class="pulse-ring pulse-ring-3"></div>
                </div>
            </div>
        }
    </div>

    <div class="timer-container">
        <div class="timer-display">
            <i class="fas fa-clock timer-icon"></i>
            <span class="timer-text">@FormatDuration(RecordingDuration)</span>
        </div>
    </div>

    @if (IsRecording || (IsPaused && IsRecording))
    {
        <div class="live-transcription">
            <div class="live-transcription-content">
                <i class="fas fa-quote-left quote-icon"></i>
                <span class="live-text">@LiveTranscriptionText</span>
                @if (IsRecording && !IsPaused)
                {
                    <span class="typing-indicator">|</span>
                }
            </div>
        </div>
    }


    <div class="controls-container">
        @if (!IsRecording && !IsPaused && !HasCompletedRecording)
        {
            <button class="btn btn-start" @onclick="StartRecording" disabled="@IsProcessing">
                <div class="btn-content">
                    <img src="_content/TeyaMobile.Shared/images/aimic.png" style="width: 24px; height: 24px;" />
                    <span class="btn-text">Start</span>
                </div>
                <div class="btn-glow"></div>
            </button>
        }
        else if (IsRecording || IsPaused)
        {
            <div class="recording-controls">
                <button class="btn btn-pause" @onclick="PauseResumeRecording" disabled="@IsProcessing">
                    <div class="btn-content">
                        <i class="fas @(IsPaused ? "fa-play" : "fa-pause") btn-icon"></i>
                        <span class="btn-text">@(IsPaused ? "Resume" : "Pause")</span>
                    </div>
                    <div class="btn-glow"></div>
                </button>

                <button class="btn btn-stop" @onclick="StopRecording" disabled="@IsProcessing">
                    <div class="btn-content">
                        <i class="fas fa-stop btn-icon"></i>
                        <span class="btn-text">Stop</span>
                    </div>
                    <div class="btn-glow"></div>
                </button>
            </div>
        }
        else if (HasCompletedRecording)
        {
            <div class="post-recording-controls">
                <button class="btn btn-rerecord" @onclick="ReRecord" disabled="@IsProcessing">
                    <div class="btn-content">
                        <i class="fas fa-redo btn-icon"></i>
                        <span class="btn-text">Re-record</span>
                    </div>
                    <div class="btn-glow"></div>
                </button>

                <button class="btn btn-process" @onclick="ProcessRecording" disabled="@IsProcessing">
                    <div class="btn-content">
                        <i class="fas fa-arrow-right btn-icon"></i>
                        <span class="btn-text">Process</span>
                    </div>
                    <div class="btn-glow"></div>
                </button>
            </div>
        }
    </div>



    <div class="transcription-container">
        <div class="transcription-header">
            <i class="fas fa-file-alt transcription-icon"></i>
            <h3>Complete Transcription</h3>
        </div>
        <div class="transcription-editor" @ref="transcriptionEditor">
            @if (string.IsNullOrEmpty(TranscriptionText))
            {
                <div class="transcription-placeholder">
                    <i class="fas fa-microphone-alt placeholder-icon"></i>
                    <span>Transcription will appear here...</span>
                </div>
            }
            else
            {
                <div class="transcription-content">
                    @TranscriptionText
                </div>
            }
        </div>
    </div>

    @if (IsProcessing)
    {
        <div class="processing-overlay">
            <div class="processing-content">
                <div class="processing-spinner">
                    <div class="spinner-ring"></div>
                    <div class="spinner-ring"></div>
                    <div class="spinner-ring"></div>
                </div>
                <h3 class="processing-title">Processing Audio</h3>
                <p class="processing-subtitle">Please wait while we process your recording...</p>
            </div>
        </div>
    }
</div>

<style>
    @@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');
    @@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    .audio-recorder-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        max-width: 2400px;
        color: black;
        margin: 0 auto;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: linear-gradient(180deg, #ffffff 0%, #d0f0f9 100%);
        @* background: linear-gradient(135deg, #ffffff 0%, #764ba2 100%); *@
        @* background: linear-gradient(135deg, #667eea 0%, #ffffff 100%); *@
        @* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); *@
        min-height: 100vh;
        position: relative;
        overflow-x: hidden;
    }

        .audio-recorder-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        .audio-recorder-container > * {
            position: relative;
            z-index: 1;
        }

    .status-header {
        margin: 20px 0 30px 0;
        transform: translateY(0);
        transition: transform 0.3s ease;
    }

    .status-indicator {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 24px;
        border-radius: 30px;
        color: white;
        font-weight: 600;
        text-align: center;
        min-width: 200px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .status-ready {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .status-recording {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        animation: pulse-glow 2s infinite;
    }

    .status-paused {
        background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
    }

    .status-processing {
        background: linear-gradient(135deg, #48cae4 0%, #023e8a 100%);
    }

    .status-icon {
        font-size: 16px;
        display: flex;
        align-items: center;
    }

    .recording-dot {
        animation: blink 1s infinite;
    }

    @@keyframes pulse-glow {
        0%, 100% {
            box-shadow: 0 8px 32px rgba(255, 107, 107, 0.3);
            transform: scale(1);
        }

        50% {
            box-shadow: 0 8px 32px rgba(255, 107, 107, 0.6);
            transform: scale(1.02);
        }
    }

    @@keyframes blink {
        0%, 50% {
            opacity: 1;
        }

        51%, 100% {
            opacity: 0.3;
        }
    }

    .animation-container {
        margin: 30px 0;
        height: 50px;
        width: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
    }

    .wave-gif {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 20px;
        opacity: 0.3;
        transition: opacity 0.3s ease;
    }

        .wave-gif.playing {
            opacity: 1;
        }

    .wave-container {
        position: relative;
        width: 100%;
        height: 70%;
    }

    .wave-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 3px;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .wave-container.active .wave-overlay {
        opacity: 0.8;
    }

    .wave-bar {
        width: 4px;
        background: linear-gradient(to top, #667eea, #764ba2, #ffffff);
        border-radius: 2px;
        height: var(--height, 20px);
        transition: height 0.2s ease;
        box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    }

    .wave-container.active .wave-bar {
        animation: wave-dance 0.6s ease-in-out infinite alternate;
    }

    @@keyframes wave-dance {
        from {
            height: 20px;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        }

        to {
            height: var(--height, 80px);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
        }
    }

    .native-animation-container {
        position: relative;
        width: 100%;
        height: 100%;
    }

    .pulse-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 120px;
        height: 120px;
    }

    .pulse-ring {
        position: absolute;
        border: 2px solid rgba(255, 255, 255, 0.6);
        border-radius: 50%;
        width: 100%;
        height: 100%;
        opacity: 0;
    }

    .pulse-overlay.active .pulse-ring {
        animation: pulse-expand 2s infinite;
    }

    .pulse-ring-1 {
        animation-delay: 0s;
    }

    .pulse-ring-2 {
        animation-delay: 0.7s;
    }

    .pulse-ring-3 {
        animation-delay: 1.4s;
    }

    @@keyframes pulse-expand {
        0% {
            transform: scale(0.5);
            opacity: 1;
        }

        100% {
            transform: scale(1.5);
            opacity: 0;
        }
    }

    .timer-container {
        margin: 20px 0;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 20px;
        padding: 15px 25px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .timer-display {
        display: flex;
        align-items: center;
        gap: 10px;
        color: black;
    }

    .timer-icon {
        font-size: 20px;
        opacity: 0.8;
    }

    .timer-text {
        font-size: 32px;
        font-weight: 700;
        font-family: 'Courier New', monospace;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .live-transcription {
        width: 100%;
        max-width: 500px;
        margin: 20px 0;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 15px 20px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        min-height: 60px;
        display: flex;
        align-items: center;
    }

    .live-transcription-content {
        display: flex;
        align-items: center;
        gap: 10px;
        width: 100%;
        color: black;
    }

    .quote-icon {
        font-size: 16px;
        opacity: 0.6;
        flex-shrink: 0;
    }

    .live-text {
        font-style: italic;
        font-size: 16px;
        line-height: 1.4;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .typing-indicator {
        animation: blink 1s infinite;
        font-weight: bold;
        font-size: 18px;
    }

    .controls-container {
        margin: 30px 0;
    }

    .post-recording-controls {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.btn-rerecord {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    box-shadow: 0 8px 32px rgba(116, 185, 255, 0.3);
}

.btn-rerecord .btn-glow {
    background: linear-gradient(135deg, #81ecec 0%, #00b894 100%);
}

.btn-process {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    box-shadow: 0 8px 32px rgba(0, 184, 148, 0.3);
}

.btn-process .btn-glow {
    background: linear-gradient(135deg, #00e676 0%, #1de9b6 100%);
}

.btn-rerecord:hover:not(:disabled) {
    transform: translateY(-3px) scale(1.05);
}

.btn-process:hover:not(:disabled) {
    transform: translateY(-3px) scale(1.05);
}

/* Responsive adjustments for post-recording controls */
@@media (max-width: 768px) {
    .post-recording-controls {
        flex-direction: column;
        gap: 15px;
    }
    
    .post-recording-controls .btn {
        min-width: 200px;
    }
}


    .recording-controls {
        display: flex;
        gap: 20px;
    }

    .btn {
        position: relative;
        padding: 0;
        border: none;
        border-radius: 30px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        overflow: hidden;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        min-width: 160px;
        height: 60px;
    }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

    .btn-content {
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        padding: 15px 25px;
        color: white;
        height: 100%;
    }

    .btn-glow {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1;
    }

    .btn-start {
        background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
        box-shadow: 0 8px 32px rgba(0, 184, 148, 0.3);
    }

        .btn-start .btn-glow {
            background: linear-gradient(135deg, #00e676 0%, #1de9b6 100%);
        }

    .btn-pause {
        background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
        box-shadow: 0 8px 32px rgba(253, 203, 110, 0.3);
    }

        .btn-pause .btn-glow {
            background: linear-gradient(135deg, #ffeb3b 0%, #ff9800 100%);
        }

    .btn-stop {
        background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
        box-shadow: 0 8px 32px rgba(253, 121, 168, 0.3);
    }

        .btn-stop .btn-glow {
            background: linear-gradient(135deg, #ff5722 0%, #f44336 100%);
        }

    .btn:hover:not(:disabled) {
        transform: translateY(-3px) scale(1.05);
    }

        .btn:hover:not(:disabled) .btn-glow {
            opacity: 1;
        }

    .btn:active:not(:disabled) {
        transform: translateY(-1px) scale(1.02);
    }

    .btn-icon {
        font-size: 18px;
    }

    .transcription-container {
        width: 100%;
        max-width: 550px;
        margin-top: 30px;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .transcription-header {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 20px 25px 15px 25px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .transcription-icon {
        font-size: 20px;
    }

    .transcription-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
    }

    .transcription-editor {
        height: 150px;
        overflow-y: auto;
        padding: 0;
        background: white;
        position: relative;
    }

    .transcription-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #999;
        gap: 10px;
    }

    .placeholder-icon {
        font-size: 40px;
        opacity: 0.5;
    }

    .transcription-content {
        padding: 20px 25px;
        line-height: 1.6;
        color: #333;
        font-size: 15px;
        white-space: pre-wrap;
        word-wrap: break-word;
    }

    .transcription-editor::-webkit-scrollbar {
        width: 6px;
    }

    .transcription-editor::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    .transcription-editor::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 3px;
    }

        .transcription-editor::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

    .processing-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        backdrop-filter: blur(5px);
    }

    .processing-content {
        text-align: center;
        color: white;
        max-width: 300px;
    }

    .processing-spinner {
        position: relative;
        width: 80px;
        height: 80px;
        margin: 0 auto 30px auto;
    }

    .spinner-ring {
        position: absolute;
        width: 100%;
        height: 100%;
        border: 3px solid transparent;
        border-top: 3px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

        .spinner-ring:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 10px;
            left: 10px;
            border-top-color: #764ba2;
            animation-duration: 1.5s;
            animation-direction: reverse;
        }

        .spinner-ring:nth-child(3) {
            width: 40px;
            height: 40px;
            top: 20px;
            left: 20px;
            border-top-color: #ffffff;
            animation-duration: 2s;
        }

    @@keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .processing-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 10px 0;
    }

    .processing-subtitle {
        font-size: 16px;
        opacity: 0.8;
        margin: 0;
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .audio-recorder-container {
            padding: 15px;
        }

        .animation-container {
            width: 280px;
            height: 50px;
        }

        .timer-text {
            font-size: 28px;
        }

        .recording-controls {
            flex-direction: column;
            gap: 15px;
        }

        .btn {
            min-width: 200px;
        }

        .transcription-container {
            max-width: 100%;
        }
    }

    @@media (max-width: 480px) {
        .status-indicator {
            min-width: 180px;
            padding: 10px 20px;
        }

        .animation-container {
            width: 250px;
            height: 50px;
        }

        .timer-text {
            font-size: 24px;
        }
    }
</style>
