﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using DotNetEnv;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using TeyaMobileViewModel.ViewModel;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public class RoleslistService : IRoleslistService
    {
        //private readonly ITokenService _tokenService;
        private readonly IAuthenticationService authenticationService;
        private readonly HttpClient _httpClient;
        private readonly string _RoleslistService;
        private readonly IStringLocalizer<RoleslistService> _localizer;
        private readonly ILogger<RoleslistService> _logger;
        private bool HasAccess { get; set; }
        public RoleslistService(HttpClient httpClient,IAuthenticationService authenticationService, IStringLocalizer<RoleslistService> localizer, ILogger<RoleslistService> logger)
        {
            //_tokenService = tokenService;
            this.authenticationService = authenticationService;
            Env.Load();
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _RoleslistService = Environment.GetEnvironmentVariable("MemberServiceURL");
        }

        /// <summary>
        /// Gets all Roleslists.
        /// </summary>
        public async Task<List<Rolesdata>> GetAllRoleslistsAsync()
        {
            try
            {
                var accessToken = await authenticationService.GetServiceSpecificTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }

                var apiUrl = $"{_RoleslistService}/api/Roleslist";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<List<Rolesdata>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllRoleslists"]);
                throw;
            }
        }

        /// <summary>
        /// Gets all Role names from Roleslist.
        /// </summary>
        public async Task<List<string>> GetAllRoleNamesAsync()
        {
            try
            {
                var rolesList = await GetAllRoleslistsAsync();
                return rolesList.Select(role => role.Rolename).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingRoleNames"]);
                throw;
            }
        }

    }
}
