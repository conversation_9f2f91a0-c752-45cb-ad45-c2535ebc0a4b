﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using StackExchange.Redis;
using Microsoft.Azure.Amqp.Framing;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;


namespace TeyaWebApp.Components.Pages
{
    public partial class Vitals : Microsoft.AspNetCore.Components.ComponentBase
    {
        [Inject]
        private PatientService _PatientService { get; set; }
       
        [Inject] ISnackbar SnackBar { get; set; }

        private Guid patientId { get; set; }
        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }

      
       
        [Inject] ISnackbar Snackbar { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;

        [Inject] IVitalService _VitalService { get; set; }

        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private ActiveUser User { get; set; }

        private string editorContent;
        private MudDialog _vitalsdialog;
        private SfRichTextEditor RichTextEditor;
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo },
        new ToolbarItemModel() { Name = "add" },
        };
        private List<string> ToolbarItems = new List<string> { "Add" };
        public string temperature { get; set; }
        public string weight { get; set; }
        public string height { get; set; }
        public string pulse { get; set; }
        private Guid? OrgID { get; set; }
        public string blood_Pressure { get; set; }
        public SfGrid<PatientVitals> VitalsGrid { get; set; }
        private List<PatientVitals> vitals { get; set; }
        private List<PatientVitals> AddList = new();
        private List<PatientVitals> DeleteList = new();

        /// <summary>
        /// Open Edit Dialog
        /// </summary>
        private void OpenAddTaskDialog()
        {
            _vitalsdialog.ShowAsync();
        }

        /// <summary>
        /// Close Edit Dialog
        /// </summary>
        private void CloseAddTaskDialog()
        {
            _vitalsdialog.CloseAsync();
        }
        bool add = false;

        /// <summary>
        /// Retrieve Vitals and set rich text editor
        /// </summary>
        /// <returns></returns>
        protected override async Task OnInitializedAsync()
        {
            patientId = PatientID;
            editorContent = TotalText;


            OrgID = OrgId;
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(OrgId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];
            vitals = await _VitalService.GetVitalsByIdAsyncAndIsActive(PatientID, OrgID, Subscription);
            StateHasChanged();


        }

        /// <summary>
        /// Event Handler for add,edit,delete
        /// </summary>
        /// <param name="args"></param>
        private void ActionCompletedHandler(ActionEventArgs<PatientVitals> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                if (args.Data != null)
                {
                    var deletedVitals = args.Data as PatientVitals;
                    var existingItem = AddList.FirstOrDefault(v => v.VitalId == deletedVitals.VitalId);

                    if (existingItem != null)
                    {
                        AddList.Remove(existingItem);
                    }
                    else
                    {
                        deletedVitals.isActive = false;
                        deletedVitals.UpdatedBy = Guid.Parse(User.id);
                        deletedVitals.UpdatedDate = DateTime.Now;    
                        DeleteList.Add(deletedVitals);
                    }
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                args.Data.VitalId = Guid.NewGuid();
                args.Data.PatientId = PatientID;
                args.Data.OrganizationId = OrgId;
                args.Data.PCPId = Guid.Parse(User.id);
                args.Data.CreatedBy = Guid.Parse(User.id);
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.CreatedDate = DateTime.Now;
                args.Data.UpdatedDate = DateTime.Now;
                args.Data.isActive = true;
                add = true;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (add)
                {
                    if (args.Data != null)
                    {
                        var addedVitals = args.Data;
                        if (addedVitals != null)
                        {
                            AddList.Add(addedVitals); // Add to AddList
                        }
                    }
                    add = false;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;


            }
        }

        private async Task ActionBeginHandler(ActionEventArgs<PatientVitals> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {

                bool? result = await DialogService.ShowMessageBox(
                  Localizer["ConfirmDelete"],
                  Localizer["DeleteConfirmationMessage"],
                  yesText: Localizer["Yes"],
                  noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
               
            }

           else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                bool hasValidationError = false;
            
                if (!string.IsNullOrEmpty(args.Data.Temperature?.ToString()) && !IsNumeric(args.Data.Temperature))
                {
                    Snackbar.Add(@Localizer["Validation.TemperatureNumericOnly"], Severity.Warning);
                    hasValidationError = true;
                }
            
                // Uncomment and use if BP validation is needed
                //if (!string.IsNullOrEmpty(args.Data.BP?.ToString()) && !IsNumeric(args.Data.BP))
                //{
                //    Snackbar.Add(@Localizer["Validation.BPNumericOnly"], Severity.Warning);
                //    hasValidationError = true;
                //}
            
                if (!string.IsNullOrEmpty(args.Data.Pulse?.ToString()) && !IsNumeric(args.Data.Pulse))
                {
                    Snackbar.Add(@Localizer["Validation.PulseNumericOnly"], Severity.Warning);
                    hasValidationError = true;
                }
            
                if (!string.IsNullOrEmpty(args.Data.Weight?.ToString()) && !IsNumeric(args.Data.Weight))
                {
                    Snackbar.Add(@Localizer["Validation.WeightNumericOnly"], Severity.Warning);
                    hasValidationError = true;
                }
            
                if (!string.IsNullOrEmpty(args.Data.Height?.ToString()) && !IsNumeric(args.Data.Height))
                {
                    Snackbar.Add(@Localizer["Validation.HeightNumericOnly"], Severity.Warning);
                    hasValidationError = true;
                }
            
                if (hasValidationError)
                {
                    args.Cancel = true;
                    return;
                }
            }

        }

        private bool IsNumeric(object value)
        {
            return double.TryParse(value?.ToString(), out _);
        }


        /// <summary>
        /// Handle BackdropClick
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Save changes to database
        /// </summary>
        /// <returns></returns>
        private async Task SaveChanges()
        {
            if (AddList.Count != 0)
            {
                await _VitalService.AddVitalAsync(AddList, OrgID, Subscription);
            }
            await _VitalService.UpdateVitalsListAsync(DeleteList, OrgID, Subscription);
            await _VitalService.UpdateVitalsListAsync(vitals, OrgID, Subscription);
            AddList.Clear();
            DeleteList.Clear();
            editorContent = GenerateRichTextContent(Data);
           await HandleDynamicComponentUpdate();
          

            Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }

        /// <summary>
        /// Undo changes
        /// </summary>
        /// <returns></returns>
        private async Task CancelChanges()
        {

            DeleteList.Clear();
            AddList.Clear();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            vitals = await _VitalService.GetVitalsByIdAsyncAndIsActive(PatientID, OrgID, Subscription);
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }


        private string GenerateRichTextContent(string Data)
        {
            string vitalsContent = string.Join(" ",
                vitals.OrderByDescending(v => v.CreatedDate)
                    .Select(v => $"<ul><li style='margin-left: 20px;'><b>{v.CreatedDate:yyyy-MM-dd}</b> : " +
                                 $"Temperature - {v.Temperature}, " +
                                 $"Blood Pressure - {v.BP}, " +
                                 $"Pulse - {v.Pulse}, " +
                                 $"Height - {v.Height}, " +
                                 $"Weight - {v.Weight}</li></ul>"));

            return $@"<div>
    <h4 style='margin-top: 20px; margin-bottom: 10px;'>Manual Content</h4>
    {Data}
    <h4 style='margin-bottom: 10px;'>Dynamic Content</h4>
    {vitalsContent}
    </div>";
        }




        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(Data);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }
    }
}
