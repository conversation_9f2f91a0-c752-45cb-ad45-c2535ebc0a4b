<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <SupportedPlatform Include="browser" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Markdig" Version="0.41.1" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.0" />
    <PackageReference Include="Microsoft.Graph" Version="5.68.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.2.1" />
	  <PackageReference Include="Syncfusion.Blazor.Schedule" Version="29.1.41" />
	  <PackageReference Include="Syncfusion.Blazor.Calendars" Version="29.1.41" />
	  <PackageReference Include="Syncfusion.Blazor.DropDowns" Version="29.1.41" />
	  <PackageReference Include="Syncfusion.Blazor.Buttons" Version="29.1.41" />
	  <PackageReference Include="Syncfusion.Blazor.Inputs" Version="29.1.41" />
	  <PackageReference Include="Syncfusion.Blazor.Popups" Version="29.1.41" />
	  <PackageReference Include="Syncfusion.Blazor.Themes" Version="29.1.41" />
	  <PackageReference Include="Syncfusion.Licensing" Version="29.1.41" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\TeyaMobileModel\TeyaMobileModel.csproj" />
    <ProjectReference Include="..\..\TeyaMobileViewModel\TeyaMobileViewModel.csproj" />
  </ItemGroup>

  <ItemGroup>
    <MauiImage Include="wwwroot\images\aimic.png" />
    <MauiImage Include="wwwroot\images\aipause.png" />
    <MauiImage Include="wwwroot\images\aiplay.png" />
    <MauiImage Include="wwwroot\images\aistop.png" />
    <MauiImage Include="wwwroot\images\aiwave.gif" />
    <MauiImage Include="wwwroot\images\bill.png" />
    <MauiImage Include="wwwroot\images\cal.png" />
    <MauiImage Include="wwwroot\images\lightbulb.png" />
    <MauiImage Include="wwwroot\images\med.png" />
    <MauiImage Include="wwwroot\images\micp.png" />
    <MauiImage Include="wwwroot\images\microphone.png" />
    <MauiImage Include="wwwroot\images\msg.png" />
    <MauiImage Include="wwwroot\images\pause.png" />
    <MauiImage Include="wwwroot\images\res.png" />
    <MauiImage Include="wwwroot\images\teyalogo.jpg" />
    <MauiImage Include="wwwroot\images\visit.png" />
  </ItemGroup>

</Project>