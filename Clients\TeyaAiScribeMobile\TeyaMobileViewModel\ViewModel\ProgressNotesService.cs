﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.TeyaUIViewModelResources;

namespace TeyaMobileViewModel.ViewModel
{
    public class ProgressNotesService : IProgressNotesService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotes;
        private readonly IConfiguration configuration;
        private readonly IAuthenticationService _authenticationService;

        public ProgressNotesService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, IAuthenticationService authenticationService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            this.configuration = configuration;
            _EncounterNotes = configuration["EncounterNotesURL"];
            _authenticationService = authenticationService;
        }

        public async Task<List<Record>> GetRecordsByPatientIdAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _authenticationService.GetAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/Records/PatientId/{id}/{OrgID}/{Subscription}";

                //var apiUrl = $"{_EncounterNotes}/api/Records/PatientId/{id}/{OrgID}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Error fetching records: {response.StatusCode}");
                }

                var responseData = await response.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(responseData) || responseData == "[]")
                    return new List<Record>();

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<Record>>(responseData, options) ?? new List<Record>();
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while retrieving records: {ex.Message}", ex);
            }
        }

        public async Task<List<Record>> GetRecordsByPCPIdAsync(Guid pcpId, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _authenticationService.GetAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/Records/ByPCP/{pcpId}/{OrgID}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Error fetching records: {response.StatusCode}");
                }

                var responseData = await response.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(responseData) || responseData == "[]")
                    return new List<Record>();

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<Record>>(responseData, options) ?? new List<Record>();
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while retrieving records: {ex.Message}", ex);
            }
        }

        public async Task<HttpResponseMessage> SaveRecordAsync(Record updatedRecord, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _authenticationService.GetAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/Records/{updatedRecord.Id}/{OrgID}/{Subscription}";

                var serializableRecord = new Record
                {
                    Id = updatedRecord.Id,
                    PatientId = updatedRecord.PatientId,
                    PatientName = updatedRecord.PatientName,
                    PCPId = updatedRecord.PCPId,
                    DateTime = updatedRecord.DateTime,
                    Notes = updatedRecord.Notes,
                    isEditable = updatedRecord.isEditable,
                    Transcription = updatedRecord.Transcription
                };

                var bodyContent = JsonSerializer.Serialize(serializableRecord, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                });

                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);

                return response;
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while Updating record: {ex.Message}", ex);
                throw;
            }
        }
    }
}

