﻿using Syncfusion.Blazor.Popups;
using Syncfusion.Blazor.Schedule;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.ViewModel;

namespace TeyaMobile.Shared.Pages
{
    public partial class Appointments
    {
        private Syncfusion.Blazor.Schedule.SfSchedule<AppointmentData>? ScheduleRef;
        private Syncfusion.Blazor.Popups.SfDialog? DatePickerDialog;
        private CancellationTokenSource? _cancellationTokenSource;
        private DialogPositionData dialogPosition = new DialogPositionData() { X = "center", Y = "center" };
        private DateTime selectedDate = DateTime.Today;
        private bool isDatePickerVisible = false;
        private bool isLoading = false;
        private string errorMessage = "";
        private string schedulerHeight = "calc(100vh - 80px)";
        private DateTime currentTime = DateTime.Now;
        private List<AppointmentData> appointments = new();
        private int totalAppointments = 0;
        private int completedAppointments = 0;
        private int pendingAppointments = 0;
        private Timer? _statusTimer;
        private enum FilterType { Total, Completed, Pending }
        private FilterType activeFilter = FilterType.Total;
        private List<AppointmentData> allAppointments = new();
        private List<AppointmentData> filteredAppointments = new();
        private bool _disposed = false;
        private Guid providerId = Guid.Empty; 
        private Guid orgId = Guid.Empty;

        protected override async Task OnInitializedAsync()
        {
            _cancellationTokenSource = new CancellationTokenSource();
            await LoadAppointments();
        }

        private async Task LoadAppointments()
        {
            if (_disposed) return;

            isLoading = true;
            errorMessage = "";
            StateHasChanged();

            try
            {
                List<Appointment> appointmentList;

                orgId = Guid.Parse("1B9C038A-3AAA-4783-A1F7-BB574E1F1BCB"); // Replace with actual organization ID after graphApi implementation
                providerId = Guid.Parse("764BC2DE-86B3-4EDB-9BDD-3E0F26C1868A"); // Replace with actual provider after graphApi implementation
                appointmentList = await AppointmentService.GetAppointmentsAsync(selectedDate, orgId, false);



                if (_disposed) return;

                appointments = appointmentList
                    .Where(a => a.AppointmentDate.Date == selectedDate.Date)
                    .Select(a => new AppointmentData
                    {
                        Id = a.Id,
                        Subject = a.PatientName ?? "Unknown Patient",
                        PatientName = a.PatientName ?? "Unknown Patient",
                        StartTime = a.StartTime ?? a.AppointmentDate,
                        EndTime = a.EndTime ?? a.AppointmentDate.AddMinutes(30),
                        Provider = a.Provider ?? "Unknown Provider",
                        VisitType = a.VisitType ?? "Consultation",
                        VisitStatus = a.VisitStatus ?? "Scheduled",
                        Reason = a.Reason ?? "",
                        Notes = a.Notes ?? "",
                        RoomNumber = a.RoomNumber ?? "00",
                        PatientId = a.PatientId,
                        Subscription = a.Subscription,
                        OrganisationId = a.OrganisationId,
                    }).ToList();

                allAppointments = appointments;
                filteredAppointments = appointments;

                UpdateSummary();
            }
            catch (HttpRequestException httpEx)
            {
                errorMessage = $"Network error: {httpEx.Message}";
                Console.WriteLine($"HTTP error loading appointments: {httpEx.Message}");
            }
            catch (Exception ex)
            {
                errorMessage = $"Error loading appointments: {ex.Message}";
                Console.WriteLine($"Error loading appointments: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                if (!_disposed)
                {
                    StateHasChanged();
                }
            }
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                _statusTimer = new Timer(_ =>
                {
                    InvokeAsync(() =>
                    {
                        UpdateSummary();
                        StateHasChanged();
                    });
                }, null, TimeSpan.Zero, TimeSpan.FromMinutes(1));
            }
        }

        private void ApplyFilter(FilterType filter)
        {
            activeFilter = filter;
            filteredAppointments = filter switch
            {
                FilterType.Completed => allAppointments
                    .Where(a => a.EndTime < currentTime ||
                           a.VisitStatus.Equals("Completed", StringComparison.OrdinalIgnoreCase))
                    .ToList(),
                FilterType.Pending => allAppointments
                    .Where(a => a.EndTime >= currentTime &&
                           !a.VisitStatus.Equals("Completed", StringComparison.OrdinalIgnoreCase) &&
                           !a.VisitStatus.Equals("Cancelled", StringComparison.OrdinalIgnoreCase))
                    .ToList(),
                _ => allAppointments.ToList()
            };
            StateHasChanged();
        }

        private async Task OpenDatePicker()
        {
            isDatePickerVisible = true;
            StateHasChanged();
        }

        private async Task OnDateSelected(DateTime selectedDateValue)
        {
            isDatePickerVisible = false;
            if (selectedDate.Date != selectedDateValue.Date)
            {
                selectedDate = selectedDateValue;
                await LoadAppointments();
            }
            StateHasChanged();
        }


        private void UpdateSummary()
        {
            currentTime = DateTime.Now;
            totalAppointments = appointments.Count;

            completedAppointments = appointments.Count(a =>
                a.EndTime < currentTime ||
                a.VisitStatus.Equals("Completed", StringComparison.OrdinalIgnoreCase));

            pendingAppointments = appointments.Count(a =>
                a.EndTime >= currentTime &&
                !a.VisitStatus.Equals("Completed", StringComparison.OrdinalIgnoreCase) &&
                !a.VisitStatus.Equals("Cancelled", StringComparison.OrdinalIgnoreCase));
        }

        private string GetAppointmentStatusClass(AppointmentData appointment)
        {
            return appointment.VisitStatus.ToLower().Replace(" ", "-") switch
            {
                "completed" => "completed",
                "cancelled" => "cancelled",
                "confirmed" => "confirmed",
                "pending" => "pending",
                _ => "scheduled"
            };
        }

        private void OnDatePickerVisibilityChanged(bool visible)
        {
            if (_disposed) return;
            isDatePickerVisible = visible;
        }

        private async Task RetryLoadAppointments()
        {
            if (_disposed) return;
            await LoadAppointments();
        }

        private async Task OnAppointmentClick(EventClickArgs<AppointmentData> args)
        {
            if (_disposed || args.Event == null) return;
            await OnAppointmentCardClick(args.Event);
        }

        private async Task OnAppointmentCardClick(AppointmentData appointment)
        {
            if (_disposed || appointment == null) return;

            try
            {
                Navigation.NavigateTo($"/recorder/{appointment.PatientId}?appointmentId={appointment.Id}&orgId={appointment.OrganisationId}&sub={appointment.Subscription}&visitType={appointment.VisitType}&providerId={providerId}");
            }
            catch (Exception ex)
            {
                if (!_disposed)
                {
                    Console.WriteLine($"Error handling appointment click: {ex.Message}");
                }
            }
        }

        public void Dispose()
        {
            _disposed = true;
            _statusTimer?.Dispose();
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
        }
    }
}
