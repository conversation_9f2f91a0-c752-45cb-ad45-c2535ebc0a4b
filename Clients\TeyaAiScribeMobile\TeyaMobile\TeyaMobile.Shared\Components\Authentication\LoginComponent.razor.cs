using Microsoft.AspNetCore.Components;
using TeyaMobileViewModel.ViewModel;

namespace TeyaMobile.Shared.Components.Authentication
{
    public partial class LoginComponent : ComponentBase
    {
        [Inject] public IAuthenticationService AuthService { get; set; } = default!;
        [Inject] public NavigationManager Navigation { get; set; } = default!;
        private bool IsLoading = false;
        private bool IsAuthenticated = false;
        private string UserName = string.Empty;
        private string UserEmail = string.Empty;
        private string ErrorMessage = string.Empty;
        protected override async Task OnInitializedAsync()
        {
            await CheckAuthenticationState();
        }

        private async Task CheckAuthenticationState()
        {
            try
            {
                IsAuthenticated = AuthService.IsAuthenticated;

                if (IsAuthenticated)
                {
                    UserName = await AuthService.GetUserNameAsync();
                    UserEmail = await AuthService.GetUserEmailAsync();
                }

                ErrorMessage = string.Empty;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error checking authentication: {ex.Message}";
                StateHasChanged();
            }
        }

        private async Task HandleLogin()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;
                StateHasChanged();

                var success = await AuthService.LoginAsync();

                if (success)
                {
                    // For Android platform - authentication successful
                    // Navigate to home page where AuthenticationHandler will process user registration
                    Navigation.NavigateTo("/", forceLoad: true);
                }
                else
                {
                    // For Web platform - login happens via redirect
                    // User registration will be handled by AuthenticationHandler after redirect
                    IsLoading = false;
                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Login failed: {ex.Message}";
                IsLoading = false;
                StateHasChanged();
            }
        }

        private async Task HandleLogout()
        {
            try
            {
                IsLoading = true;
                StateHasChanged();

                await AuthService.LogoutAsync();

                IsAuthenticated = false;
                UserName = string.Empty;
                UserEmail = string.Empty;
                ErrorMessage = string.Empty;
                IsLoading = false;

                StateHasChanged();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Logout failed: {ex.Message}";
                IsLoading = false;
                StateHasChanged();
            }
        }


        private void ClearError()
        {
            ErrorMessage = string.Empty;
            StateHasChanged();
        }
    }
}
