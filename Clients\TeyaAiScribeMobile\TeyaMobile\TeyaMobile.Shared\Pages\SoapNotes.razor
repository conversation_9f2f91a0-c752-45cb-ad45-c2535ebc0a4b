﻿@page "/soapnotes/{PatientId:guid}"
@page "/soapnotes"
@using TeyaMobileModel.Model
@using TeyaMobileViewModel.ViewModel
@using System.Text.Json
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Notifications
@inject IProgressNotesService NotesService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@implements IDisposable

<PageTitle>Patient Notes</PageTitle>

<div class="soap-notes-container">
    <!-- Header -->
    <div class="soap-header">
        <div class="header-content">
            <div class="title-section">
                <h3>Patient Name: @(!string.IsNullOrEmpty(currentPatientName) ? currentPatientName : "Unknown Patient")</h3>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    @if (isLoading)
    {
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <span>Loading Notes...</span>
        </div>
    }

    <!-- Content -->
    <div class="soap-content">
        @if (soapRecords.Any())
        {
            <!-- Dynamic SOAP Notes from Database -->
            @foreach (var record in soapRecords)
            {
                <div class="record-card">
                    <div class="record-header">
                        <div class="record-info">
                            <span class="record-date">@record.DateTime.ToString("MMM dd, yyyy HH:mm")</span>
                            <span class="patient-name">@record.PatientName</span>
                        </div>
                    </div>

                    @if (record.ProcessedNotes != null)
                    {
                        @foreach (var soapSection in record.ProcessedNotes)
                        {
                            <div class="soap-section">
                                <div class="section-header">
                                    <h4>@soapSection.Key</h4>
                                    <div class="section-actions">
                                        <Syncfusion.Blazor.Buttons.SfButton CssClass="expand-btn"
                                                                            IconCss="@(expandedSections.Contains($"{record.Id}_{soapSection.Key}") ? "e-icons e-chevron-up" : "e-icons e-chevron-down")"
                                                                            OnClick="() => ToggleSection(record.Id, soapSection.Key)">
                                        </Syncfusion.Blazor.Buttons.SfButton>
                                    </div>
                                </div>

                                <div class="section-content @(expandedSections.Contains($"{record.Id}_{soapSection.Key}") ? "expanded" : "collapsed")">
                                    @foreach (var item in soapSection.Value)
                                    {
                                        <div class="soap-field">
                                            <div class="field-header">
                                                <label class="field-label">@item.Key</label>
                                            </div>

                                            <div class="field-content">
                                                <div class="field-display @(string.IsNullOrEmpty(item.Value.Value) ? "empty" : "")">
                                                    @if (string.IsNullOrEmpty(item.Value.Value))
                                                    {
                                                        <span class="placeholder-text">No data available</span>
                                                    }
                                                    else
                                                    {
                                                        <div class="markdown-content">
                                                            @item.Value
                                                        </div>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        }
                    }

                    else if (!string.IsNullOrEmpty(record.Notes))
                    {
                        <!-- Display raw notes if parsing fails -->
                        <div class="raw-notes-section">
                            <div class="section-header">
                                <h4>Notes</h4>
                            </div>
                            <div class="raw-notes-content">
                                <pre class="raw-notes-text">@record.Notes</pre>
                            </div>
                        </div>
                    }
                </div>
            }
        }
        else if (!isLoading)
        {
            <!-- Error Message when no data is found -->
            <div class="error-container">
                <div class="error-message">
                    <i class="e-icons e-warning"></i>
                    <h4>Sorry Unable to process your request</h4>
                    <p>No notes could be retrieved at this time.</p>
                </div>
            </div>
        }
    </div>
</div>

<style>
    .soap-notes-container {
        height: 100vh;
        display: flex;
        flex-direction: column;
        background: #f8f9fa;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .soap-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        z-index: 100;
    }

    .header-content {
        display: flex;
        align-items: center;
        justify-content: center;
        max-width: 1200px;
        margin: 0 auto;
    }

    .title-section {
        text-align: center;
    }

        .title-section h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 20px;
        background: white;
        margin: 20px;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .markdown-content {
    line-height: 1.6;
    color: #495057;
    }

    .markdown-content h1,
    .markdown-content h2,
    .markdown-content h3,
    .markdown-content h4,
    .markdown-content h5,
    .markdown-content h6 {
        margin-top: 1em;
        margin-bottom: 0.5em;
        font-weight: 600;
    }

    .markdown-content p {
        margin-bottom: 1em;
    }

    .markdown-content strong {
        font-weight: 600;
    }

    .markdown-content em {
        font-style: italic;
    }

    .markdown-content ul,
    .markdown-content ol {
        margin-bottom: 1em;
        padding-left: 1.5em;
    }

    .markdown-content li {
        margin-bottom: 0.25em;
    }

    .markdown-content blockquote {
        border-left: 4px solid #dee2e6;
        padding-left: 1em;
        margin: 1em 0;
        color: #6c757d;
        font-style: italic;
    }

    .markdown-content code {
        background-color: #f8f9fa;
        padding: 0.2em 0.4em;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
    }

    .markdown-content pre {
        background-color: #f8f9fa;
        padding: 1em;
        border-radius: 6px;
        overflow-x: auto;
        margin: 1em 0;
    }

    .markdown-content pre code {
        background: none;
        padding: 0;
    }


    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
    }

    @@keyframes spin {
        0%

    {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }

    }

    .soap-content {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
        width: 100%;
    }

    .record-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }

    .record-header {
        background: #f8f9fa;
        padding: 12px 16px;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .record-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .record-date {
        font-weight: 600;
        color: #495057;
        font-size: 14px;
    }

    .patient-name {
        font-size: 12px;
        color: #6c757d;
    }

    .soap-section {
        border-bottom: 1px solid #e9ecef;
    }

        .soap-section:last-child {
            border-bottom: none;
        }

    .section-header {
        padding: 16px;
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.2s;
    }

        .section-header:hover {
            background: #e9ecef;
        }

        .section-header h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #495057;
        }

    .expand-btn {
        background: transparent !important;
        border: none !important;
        color: #6c757d !important;
        min-width: 32px !important;
        height: 32px !important;
    }

    .section-content {
        overflow: hidden;
        transition: max-height 0.3s ease-in-out;
    }

        .section-content.collapsed {
            max-height: 0;
        }

        .section-content.expanded {
            max-height: 1000px;
        }

    .soap-field {
        padding: 16px;
        border-bottom: 1px solid #f1f3f4;
    }

        .soap-field:last-child {
            border-bottom: none;
        }

    .field-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .field-label {
        font-weight: 600;
        color: #495057;
        font-size: 14px;
    }

    .field-content {
        min-height: 40px;
    }

    .field-display {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 12px;
        min-height: 80px;
    }

        .field-display.empty {
            border-style: dashed;
            display: flex;
            align-items: center;
            justify-content: center;
        }

    .placeholder-text {
        color: #6c757d;
        font-style: italic;
        font-size: 14px;
    }

    .field-text {
        margin: 0;
        white-space: pre-wrap;
        font-family: inherit;
        color: #495057;
        line-height: 1.5;
    }

    .raw-notes-section {
        padding: 16px;
    }

    .raw-notes-content {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 12px;
        margin-top: 8px;
    }

    .raw-notes-text {
        margin: 0;
        white-space: pre-wrap;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        color: #495057;
        line-height: 1.4;
    }

    .error-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 60px 20px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-left: 4px solid #dc3545;
    }

    .error-message {
        text-align: center;
        color: #6c757d;
    }

        .error-message i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #dc3545;
        }

        .error-message h4 {
            margin: 0 0 8px 0;
            font-size: 18px;
            color: #dc3545;
            font-weight: 600;
        }

        .error-message p {
            margin: 0;
            font-size: 14px;
            color: #6c757d;
        }

    /* Mobile Responsive */
    @@media (max-width: 768px) {
        .soap-content

    {
        padding: 12px;
    }

    .header-content {
        flex-wrap: wrap;
        gap: 8px;
    }

    .title-section h3 {
        font-size: 16px;
    }

    .section-header h4 {
        font-size: 14px;
    }

    .field-label {
        font-size: 13px;
    }

    .record-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    }
</style>
