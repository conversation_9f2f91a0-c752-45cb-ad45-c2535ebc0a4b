﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class TherapeuticInterventions
    {
        [Inject] public ITherapeuticInterventionsListService _TherapeuticInterventionsListService { get; set; }
        [Inject] public ITherapeuticInterventionsService _TherapeuticInterventionsService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        public string TherapeuticInterventionsListName { get; set; }
        private string editorContent;
        private SfRichTextEditor RichTextEditor;
        private MudDialog __TherapeuticInterventions;
        private Guid PatientId { get; set; }
        private DateTime? _CreatedDate;
        private Guid? OrgID { get; set; }
        private List<TherapeuticInterventionsListCode> _TherapeuticInterventionsListCodes { get; set; } = new List<TherapeuticInterventionsListCode>();
        public SfGrid<TeyaUIModels.Model.TherapeuticInterventionsData> TherapeuticInterventionsGrid { get; set; }

        private List<TeyaUIModels.Model.TherapeuticInterventionsData> AddList = new();
        private List<TeyaUIModels.Model.TherapeuticInterventionsData> _TherapeuticInterventions { get; set; }
        private List<TeyaUIModels.Model.TherapeuticInterventionsData> deleteTherapeuticInterventionslist { get; set; } = new List<TeyaUIModels.Model.TherapeuticInterventionsData>();
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" }
         };

        /// <summary>
        /// Get All TherapeuticInterventionsList Codes and Description from Database
        /// </summary>
        /// <returns></returns>
        protected override async Task OnInitializedAsync()
        {
            PatientId = _PatientService.PatientData.Id;
            OrgID = _PatientService.PatientData.OrganizationID;
            activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];
            _TherapeuticInterventionsListCodes = await _TherapeuticInterventionsListService.GetAllTherapeuticInterventionsListCodesAsync();
            _TherapeuticInterventions = await _TherapeuticInterventionsService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription);
            UpdateEditorContent();    
        }

        /// <summary>
        /// Handle backdrop click
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private void UpdateEditorContent()
        {
            editorContent = string.Join("<br>", _TherapeuticInterventions
                .OrderByDescending(s => s.CreatedDate)
                .Select(s => $@"<p> <strong> {(s.CreatedDate.HasValue ? s.CreatedDate.Value.ToString("MM-dd-yyyy") : Localizer["No date"])} : </strong> {Localizer["Therapy Type"]}: {s.TherapyType} , {Localizer["Notes"]}:  {s.Notes}</p>"));
        }


        /// <summary>
        /// Open Dailog
        /// </summary>
        /// <returns></returns>
        private async Task OpenNewDialogBox()
        {
            await __TherapeuticInterventions.ShowAsync();
        }

        /// <summary>
        /// close Dailog
        /// </summary>
        /// <returns></returns>
        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await __TherapeuticInterventions.CloseAsync();
        }

        /// <summary>
        /// Search Function to get TherapeuticInterventionsList Codes and Description 
        /// </summary>
        /// <param name="value"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected async Task<IEnumerable<string>> SearchTherapeuticInterventionsListCodes(string value, CancellationToken cancellationToken)
        {
            var searchResults = _TherapeuticInterventionsListCodes
        .Where(t => !string.IsNullOrEmpty(t.TherapyType) && t.TherapyType.Contains(value, StringComparison.OrdinalIgnoreCase))
        .Select(t => t.TherapyType)
        .Distinct()
        .ToList();

            cancellationToken.ThrowIfCancellationRequested();
            return searchResults;
        }

        /// <summary>
        /// Add new Surgery and update it to the database
        /// </summary>
        private async void AddNewTherapyType()
        {
            var newTherapyType = new TeyaUIModels.Model.TherapeuticInterventionsData
            {
                TherapeuticInterventionsID = Guid.NewGuid(),
                PatientId = PatientId,
                PCPId = Guid.Parse(User.id),
                OrganizationId = _PatientService.PatientData.OrganizationID,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                TherapyType = TherapeuticInterventionsListName,
                IsActive = true,
            };

            AddList.Add(newTherapyType);
            _TherapeuticInterventions.Add(newTherapyType);
            await TherapeuticInterventionsGrid.Refresh();
            ResetInputFields();
        }

        /// <summary>
        /// Clear the fields for closure
        /// </summary>
        private async void ResetInputFields()
        {
            TherapeuticInterventionsListName = string.Empty;
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Save removed rows locally in SFgrid
        /// </summary>
        public void ActionCompletedHandler(ActionEventArgs<TeyaUIModels.Model.TherapeuticInterventionsData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deleleTherapeuticInterventions = args.Data;
                var existingItem = AddList.FirstOrDefault(c => c.TherapeuticInterventionsID == deleleTherapeuticInterventions.TherapeuticInterventionsID);
                args.Data.IsActive = false;

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    deleleTherapeuticInterventions.IsActive = false;
                    deleleTherapeuticInterventions.UpdatedDate = DateTime.Now;
                    deleteTherapeuticInterventionslist.Add(deleleTherapeuticInterventions);
                }

            }
        }

        public async Task ActionBeginHandler(ActionEventArgs<TeyaUIModels.Model.TherapeuticInterventionsData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                  Localizer["ConfirmDelete"],
                  Localizer["DeleteConfirmationMessage"],
                  yesText: Localizer["Yes"],
                  noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
        }

        /// <summary>
        ///  Save function to save the data in database (Fron-ent 'Save' Button)
        /// </summary>
        /// <returns></returns>
        private async Task SaveData()
        {
            if (AddList.Count != 0)
            {
                await _TherapeuticInterventionsService.AddTherapeuticInterventionsAsync(AddList, OrgID, Subscription);
            }
            await _TherapeuticInterventionsService.UpdateTherapeuticInterventionsListAsync(_TherapeuticInterventions, OrgID, Subscription);
            await _TherapeuticInterventionsService.UpdateTherapeuticInterventionsListAsync(deleteTherapeuticInterventionslist, OrgID, Subscription);
            deleteTherapeuticInterventionslist.Clear();
            AddList.Clear();
            UpdateEditorContent();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
            Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
        }

        /// <summary>
        /// To Undo Changes
        /// </summary>
        /// <returns></returns>
        private async Task CancelData()
        {
            deleteTherapeuticInterventionslist.Clear();
            AddList.Clear();
            _TherapeuticInterventions = await _TherapeuticInterventionsService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
        }

        /// <summary>
        /// Update Value in TherapeuticInterventionsList Name List
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private async Task OnTherapeuticInterventionsListNameChanged(string value)
        {
            TherapeuticInterventionsListName = value;
            StateHasChanged();
        }
    }
}