using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Identity.Web;
using TeyaMobile.Shared.Services;
using System.Security.Claims;

namespace TeyaMobile.Web.Services
{
    /// <summary>
    /// Simplified web authentication service using Microsoft Identity
    /// </summary>
    public class SimpleWebAuthenticationService : TeyaMobileViewModel.ViewModel.IAuthenticationService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<SimpleWebAuthenticationService> _logger;
        private readonly ITokenAcquisition _tokenAcquisition;

        public SimpleWebAuthenticationService(
            IHttpContextAccessor httpContextAccessor,
            IServiceProvider serviceProvider,
            ILogger<SimpleWebAuthenticationService> logger,
            ITokenAcquisition tokenAcquisition)
        {
            _httpContextAccessor = httpContextAccessor;
            _serviceProvider = serviceProvider;
            _logger = logger;
            _tokenAcquisition = tokenAcquisition;
        }

        public bool IsAuthenticated =>
            _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;

        public Task<bool> LoginAsync()
        {
            try
            {
                // Check if user is already authenticated
                if (IsAuthenticated)
                {
                    return Task.FromResult(true);
                }

                var navigationManager = _serviceProvider.GetRequiredService<NavigationManager>();

                // Trigger the authentication challenge
                navigationManager.NavigateTo("/MicrosoftIdentity/Account/SignIn", forceLoad: true);

                // Return false since authentication is in progress via redirect
                return Task.FromResult(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Login failed: {Error}", ex.Message);
                return Task.FromResult(false);
            }
        }

        public Task LogoutAsync()
        {
            try
            {
                var navigationManager = _serviceProvider.GetRequiredService<NavigationManager>();

                // Trigger the logout
                navigationManager.NavigateTo("/MicrosoftIdentity/Account/SignOut", forceLoad: true);

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Logout failed: {Error}", ex.Message);
                throw;
            }
        }

        public async Task<string> GetAccessTokenAsync()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext == null || !IsAuthenticated)
                    return string.Empty;

                // Use Graph API scope by default with OBO flow
                // This automatically handles refresh if the token is expired
                try
                {
                    var scopes = new[] { "https://graph.microsoft.com/.default" };
                    var accessToken = await _tokenAcquisition.GetAccessTokenForUserAsync(scopes);
                    if (!string.IsNullOrEmpty(accessToken))
                    {
                        _logger.LogInformation("Successfully acquired Graph API token using OBO flow");
                        return accessToken;
                    }
                }
                catch (Exception tokenEx)
                {
                    _logger.LogWarning(tokenEx, "Graph API token acquisition failed, falling back to stored token");
                }

                // Fallback: Get access token from authentication properties
                var storedToken = await httpContext.GetTokenAsync("access_token");
                return storedToken ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get access token");
                return string.Empty;
            }
        }

        public Task<string> GetUserNameAsync()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext?.User?.Identity?.IsAuthenticated != true)
                    return Task.FromResult(string.Empty);

                var claims = httpContext.User.Claims;
                var displayName = claims.FirstOrDefault(c => c.Type == "name")?.Value ??
                                 claims.FirstOrDefault(c => c.Type == ClaimTypes.Name)?.Value ?? 
                                 string.Empty;

                return Task.FromResult(displayName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user name");
                return Task.FromResult(string.Empty);
            }
        }

        public Task<string> GetUserEmailAsync()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext?.User?.Identity?.IsAuthenticated != true)
                    return Task.FromResult(string.Empty);

                var claims = httpContext.User.Claims;
                var email = claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)?.Value ??
                           claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value ??
                           string.Empty;

                return Task.FromResult(email);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user email");
                return Task.FromResult(string.Empty);
            }
        }

        public async Task<string> GetGraphApiScopeAsync()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext == null || !IsAuthenticated)
                    return string.Empty;

                // Use the Graph_Auth_Scope from configuration (https://graph.microsoft.com/.default)
                var graphScopes = new[] { "https://graph.microsoft.com/.default" };

                try
                {
                    var graphApiToken = await _tokenAcquisition.GetAccessTokenForUserAsync(graphScopes);

                    if (!string.IsNullOrEmpty(graphApiToken))
                    {
                        _logger.LogInformation("Successfully acquired Graph API token using ITokenAcquisition with .default scope");
                        return graphApiToken;
                    }
                }
                catch (MicrosoftIdentityWebChallengeUserException ex)
                {
                    _logger.LogWarning(ex, "Graph API token requires user consent. Triggering authentication challenge.");

                    // The user needs to consent to Graph API scopes
                    // Trigger a re-authentication with the required scopes
                    var navigationManager = _serviceProvider.GetRequiredService<NavigationManager>();

                    // Build the challenge URL with the required scopes
                    var challengeUrl = $"/MicrosoftIdentity/Account/Challenge?scope={Uri.EscapeDataString("https://graph.microsoft.com/.default")}";
                    navigationManager.NavigateTo(challengeUrl, forceLoad: true);

                    return string.Empty;
                }
                catch (Exception tokenEx)
                {
                    _logger.LogWarning(tokenEx, "Token acquisition failed for Graph API: {Error}", tokenEx.Message);
                }

                _logger.LogWarning("Failed to acquire Graph API token using ITokenAcquisition");
                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get Graph API access token: {Error}", ex.Message);
                return string.Empty;
            }
        }

        public async Task<string> GetServiceSpecificTokenAsync()
        {
            try
            {
                // Use client credentials flow for service-specific API access
                var authority = Environment.GetEnvironmentVariable("AUTH_AUTHORITY");
                var clientId = Environment.GetEnvironmentVariable("AUTH_CLIENT_ID");
                var clientSecret = Environment.GetEnvironmentVariable("AUTH_CLIENT_SECRET");

                if (string.IsNullOrEmpty(authority) || string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(clientSecret))
                {
                    _logger.LogWarning("AUTH credentials not configured for client credentials flow");
                    return string.Empty;
                }

                // Use the CIAM authority for token endpoint
                var tokenUrl = "https://TeyaHealthDevAuth.ciamlogin.com/03a052f6-4a19-4ae7-8ed7-47b794e0e597/oauth2/v2.0/token";
                var httpClient = new HttpClient();
                
                var content = new FormUrlEncodedContent(new[]
                {
                    new KeyValuePair<string, string>("grant_type", "client_credentials"),
                    new KeyValuePair<string, string>("client_id", clientId),
                    new KeyValuePair<string, string>("client_secret", clientSecret),
                    new KeyValuePair<string, string>("scope", "api://e21369d6-92b3-446b-b981-0291bcb29b1b/.default")
                });

                var response = await httpClient.PostAsync(tokenUrl, content);
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to acquire service-specific token: {StatusCode} - {Error}", response.StatusCode, errorContent);
                    return string.Empty;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var tokenResponse = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(responseContent);

                if (tokenResponse != null && tokenResponse.TryGetValue("access_token", out var accessToken))
                {
                    _logger.LogInformation("Successfully acquired service-specific token using client credentials flow");
                    return accessToken.ToString() ?? string.Empty;
                }

                _logger.LogWarning("Failed to parse service-specific token response");
                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get service-specific access token: {Error}", ex.Message);
                return string.Empty;
            }
        }
    }
}
