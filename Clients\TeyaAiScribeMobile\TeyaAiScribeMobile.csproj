﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net9.0-android;net9.0-ios;net9.0-maccatalyst</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net9.0-tizen</TargetFrameworks> -->

		<!-- Note for MacCatalyst:
		The default runtime is maccatalyst-x64, except in Release config, in which case the default is maccatalyst-x64;maccatalyst-arm64.
		When specifying both architectures, use the plural <RuntimeIdentifiers> instead of the singular <RuntimeIdentifier>.
		The Mac App Store will NOT accept apps with ONLY maccatalyst-arm64 indicated;
		either BOTH runtimes must be indicated or ONLY macatalyst-x64. -->
		<!-- For example: <RuntimeIdentifiers>maccatalyst-x64;maccatalyst-arm64</RuntimeIdentifiers> -->

		<OutputType>Exe</OutputType>
		<RootNamespace>TeyaAiScribeMobile</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Display name -->
		<ApplicationTitle>TeyaAiScribeMobile</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.companyname.teyaaiscribemobile</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<!-- To develop, package, and publish an app to the Microsoft Store, see: https://aka.ms/MauiTemplateUnpackaged -->
		<WindowsPackageType>None</WindowsPackageType>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
	</PropertyGroup>

	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />

		<!-- Custom Fonts -->

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup>
	  <AndroidResource Remove="Resources\GlobalFiles\**" />
	  <AndroidResource Remove="Resources\NewFolder\**" />
	  <Compile Remove="Resources\GlobalFiles\**" />
	  <Compile Remove="Resources\NewFolder\**" />
	  <EmbeddedResource Remove="Resources\GlobalFiles\**" />
	  <EmbeddedResource Remove="Resources\NewFolder\**" />
	  <MauiCss Remove="Resources\GlobalFiles\**" />
	  <MauiCss Remove="Resources\NewFolder\**" />
	  <MauiXaml Remove="Resources\GlobalFiles\**" />
	  <MauiXaml Remove="Resources\NewFolder\**" />
	  <None Remove="Resources\GlobalFiles\**" />
	  <None Remove="Resources\NewFolder\**" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Remove="AppointmentService.cs" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="CommunityToolkit.Maui" Version="10.0.0" />
		<PackageReference Include="EJL.MauiHybridWebView" Version="1.0.0-preview7" />
		<PackageReference Include="Markdig" Version="0.41.1" />
		<PackageReference Include="Microsoft.Identity.Client" Version="4.66.2" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="9.0.21" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />
		<PackageReference Include="Microsoft.Maui.Essentials" Version="9.0.21" />
		<PackageReference Include="Plugin.Maui.Audio" Version="3.0.1" />
		<PackageReference Include="Syncfusion.Maui.Core" Version="29.1.38" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="SignIn.xaml.cs">
	    <DependentUpon>SignIn.xaml</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <MauiXaml Update="Appointments.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="MainPage.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Message.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="UserSettings.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	</ItemGroup>

</Project>
