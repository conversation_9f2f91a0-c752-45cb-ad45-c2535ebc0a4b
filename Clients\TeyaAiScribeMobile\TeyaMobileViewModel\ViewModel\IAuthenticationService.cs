﻿namespace TeyaMobileViewModel.ViewModel
{
    /// <summary>
    /// Simplified authentication service interface for all platforms
    /// </summary>
    public interface IAuthenticationService
    {
        /// <summary>
        /// Performs interactive login
        /// </summary>
        Task<bool> LoginAsync();

        /// <summary>
        /// Logs out the current user
        /// </summary>
        Task LogoutAsync();

        /// <summary>
        /// Gets a valid access token (refreshes if needed)
        /// </summary>
        Task<string> GetAccessTokenAsync();

        /// <summary>
        /// Gets the current user's display name
        /// </summary>
        Task<string> GetUserNameAsync();

        /// <summary>
        /// Gets the current user's email
        /// </summary>
        Task<string> GetUserEmailAsync();

        /// <summary>
        /// Checks if user is currently authenticated
        /// </summary>
        bool IsAuthenticated { get; }

        /// <summary>
        /// Gets an access token with Graph API scope for Microsoft Graph operations
        /// </summary>
        Task<string> GetGraphApiScopeAsync();

        /// <summary>
        /// Gets an access token for service-specific API operations using client credentials flow
        /// </summary>
        Task<string> GetServiceSpecificTokenAsync();

    }
}
