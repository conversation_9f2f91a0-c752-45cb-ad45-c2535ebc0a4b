﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaMobileViewModel.ViewModel;

namespace TeyaUIViewModels.ViewModel
{
    public class GraphAdminService:IGraphAdminService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<GraphAdminService> _logger;
        private readonly string _tenantId;
        private readonly string _clientId;
        private readonly string _clientSecret;
        private string _adminAccessToken;
        private DateTime _tokenExpiration = DateTime.MinValue;

        public GraphAdminService(HttpClient httpClient, ILogger<GraphAdminService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            // Environment variables with fallback values for Android compatibility
            _tenantId = Environment.GetEnvironmentVariable("ServicePrincipleTenantId") ?? "03a052f6-4a19-4ae7-8ed7-47b794e0e597";
            _clientId = Environment.GetEnvironmentVariable("ServicePrincipleClientId") ?? "d20b72c2-619c-4b74-bb31-2194e8e5a137";
            _clientSecret = Environment.GetEnvironmentVariable("ServicePrincipleSecret") ?? "****************************************";

            // Debug logging to verify values are loaded
            _logger.LogInformation($"GraphAdminService Debug - TenantId: '{_tenantId}'");
            _logger.LogInformation($"GraphAdminService Debug - ClientId: '{_clientId}'");
            _logger.LogInformation($"GraphAdminService Debug - ClientSecret: '{(_clientSecret?.Length > 0 ? "***LOADED***" : "NULL")}'");
        }

        public async Task<string> GetAdminAccessTokenAsync()
        {
            if (_adminAccessToken != null && DateTime.UtcNow < _tokenExpiration.AddMinutes(-5))
            {
                _logger.LogInformation("GraphAdminService Debug - Using cached admin token");
                return _adminAccessToken;
            }

            var tokenUrl = $"https://login.microsoftonline.com/{_tenantId}/oauth2/v2.0/token";
            _logger.LogInformation($"GraphAdminService Debug - Token URL: '{tokenUrl}'");
            var content = new FormUrlEncodedContent(new[]
            {
            new KeyValuePair<string, string>("grant_type", "client_credentials"),
            new KeyValuePair<string, string>("client_id", _clientId),
            new KeyValuePair<string, string>("client_secret", _clientSecret),
            new KeyValuePair<string, string>("scope", "https://graph.microsoft.com/.default")
        });

            var response = await _httpClient.PostAsync(tokenUrl, content);
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"Failed to get admin token: {response.StatusCode}");
            }

            var tokenResponse = await JsonSerializer.DeserializeAsync<JsonElement>(await response.Content.ReadAsStreamAsync());
            _adminAccessToken = tokenResponse.GetProperty("access_token").GetString();
            _tokenExpiration = DateTime.UtcNow.AddSeconds(tokenResponse.GetProperty("expires_in").GetInt32());

            return _adminAccessToken;
        }

        
    }
}
