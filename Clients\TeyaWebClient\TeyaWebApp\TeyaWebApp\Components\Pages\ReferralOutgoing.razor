﻿@page "/ReferralOutgoing"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject HttpClient Http
@using Syncfusion.Blazor.RichTextEditor


<SfRichTextEditor Value="@editorContent" @ref="RichTextEditor">
    <RichTextEditorToolbarSettings Items="@Tools">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="add">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline" Size="Size.Small" @onclick="OpenAddTaskDialog" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>

<MudDialog @ref="_ReferralOutgoingdialog" Style="width: 85vw; max-width: 1200px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["ReferralOutgoing"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelChanges" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
<DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">

            <SfGrid @ref="ReferralOutgoingGrid"
                    TValue="PatientReferralOutgoing"
                Style="font-size: 0.85rem; margin-top: 24px;"
                    DataSource="@ReferralOutgoings"
                AllowPaging="true"
                    Toolbar="@ToolbarItems"
                    GridLines="GridLine.Both"
                PageSettings-PageSize="5">
            <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
            <GridPageSettings PageSize="10"></GridPageSettings>
                <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="PatientReferralOutgoing"></GridEvents>
            <GridColumns>
                    <GridColumn Field="PlanReferralId" IsPrimaryKey="true" Visible="false"></GridColumn>
                    <GridColumn Field="CreatedDate" HeaderText="@Localizer["Date"]" TextAlign="TextAlign.Center" Width="120" Format="MM/dd/y"></GridColumn>
                    <GridColumn Field="TreatmentPlan" HeaderText="@Localizer["TreatmentPlan"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                    <GridColumn Field="Tests" HeaderText="@Localizer["Tests"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                    <GridColumn Field="ReferralFrom" HeaderText="@Localizer["ReferralFrom"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                    <GridColumn Field="ReferralTo" HeaderText="@Localizer["ReferralTo"]" TextAlign="TextAlign.Center" Width="100"></GridColumn>
                    <GridColumn Field="ReferralReason" HeaderText="@Localizer["ReferralReason"]" TextAlign="TextAlign.Center" Width="100"></GridColumn>
                    <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="100">
                    <GridCommandColumns>
                        <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                    </GridCommandColumns>
                </GridColumn>
            </GridColumns>
        </SfGrid>
                    <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                <MudButton Color="Color.Secondary"
                           Variant="Variant.Outlined"
                           OnClick="CancelChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Cancel"]
                </MudButton>
                <MudButton Color="Color.Primary"
                           Variant="Variant.Filled"
                           OnClick="SaveChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Save"]
                </MudButton>
            </div>
        </div>
</DialogContent>

</MudDialog>



