﻿namespace TeyaMobileModel.Model
{
    public class AppointmentData : IModel
    {
        public Guid Id { get; set; }
        public string Subject { get; set; } = "";
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string PatientName { get; set; } = "";
        public string Provider { get; set; } = "";
        public string VisitType { get; set; } = "";
        public string VisitStatus { get; set; } = "";
        public string Reason { get; set; } = "";
        public string Notes { get; set; } = "";
        public string RoomNumber { get; set; } = "";
        public Guid PatientId { get; set; }
        public bool IsAllDay { get; set; } = false;
        public bool Subscription { get; set; }
        public Guid? OrganisationId { get; set; }
    }
}
