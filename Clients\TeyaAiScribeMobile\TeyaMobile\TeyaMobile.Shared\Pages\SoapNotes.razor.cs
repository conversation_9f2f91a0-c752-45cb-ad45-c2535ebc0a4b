﻿using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaMobileModel.Model;
using Markdig; // Add this using statement

namespace TeyaMobile.Shared.Pages
{
    public partial class SoapNotes
    {
        [Parameter] public Guid PatientId { get; set; }
        [SupplyParameterFromQuery(Name = "orgId")]
        public Guid? OrganizationId { get; set; }
        [SupplyParameterFromQuery(Name = "sub")]
        public bool Subscription { get; set; }

        private List<SoapRecord> soapRecords = new();
        private HashSet<string> expandedSections = new();

        private bool isLoading = false;
        private bool _disposed = false;
        private string currentPatientName = "";

        // Add Markdig pipeline for advanced markdown processing
        private static readonly MarkdownPipeline Pipeline = new MarkdownPipelineBuilder()
            .UseAdvancedExtensions()
            .Build();

        // Platform detection
        private bool IsWeb => OperatingSystem.IsBrowser();

        public class SoapRecord
        {
            public Guid Id { get; set; }
            public Guid OrganizationId { get; set; }
            public Guid PCPId { get; set; }
            public Guid PatientId { get; set; }
            public string? PatientName { get; set; }
            public DateTime DateTime { get; set; }
            public string? Notes { get; set; }
            public string? Transcription { get; set; }
            public bool? isEditable { get; set; }
            public bool Subscription { get; set; }
            public Dictionary<string, Dictionary<string, string>>? ParsedNotes { get; set; }

            // Add property for processed HTML content
            public Dictionary<string, Dictionary<string, MarkupString>>? ProcessedNotes { get; set; }
        }

        protected override async Task OnInitializedAsync()
        {
            await LoadSoapNotes();
        }

        private async Task LoadSoapNotes()
        {
            if (_disposed) return;

            isLoading = true;
            StateHasChanged();

            try
            {
                List<Record> records = new();

                records = await NotesService.GetRecordsByPatientIdAsync(PatientId, OrganizationId, Subscription);

                soapRecords = records
                    .Where(r => !string.IsNullOrEmpty(r.Notes))
                    .Select(r => new SoapRecord
                    {
                        Id = r.Id,
                        OrganizationId = r.OrganizationId,
                        PCPId = r.PCPId,
                        PatientId = r.PatientId,
                        PatientName = r.PatientName,
                        DateTime = r.DateTime,
                        Notes = r.Notes,
                        Transcription = r.Transcription,
                        isEditable = r.isEditable,
                        Subscription = r.Subscription,
                        ParsedNotes = ParseNotesJson(r.Notes),
                        ProcessedNotes = ProcessMarkdownNotes(ParseNotesJson(r.Notes)) // Process markdown
                    })
                    .OrderByDescending(r => r.DateTime)
                    .ToList();

                if (soapRecords.Any() && !string.IsNullOrEmpty(soapRecords.First().PatientName))
                {
                    currentPatientName = soapRecords.First().PatientName;
                }

                foreach (var record in soapRecords)
                {
                    if (record.ProcessedNotes?.Any() == true)
                    {
                        var firstSection = record.ProcessedNotes.First().Key;
                        expandedSections.Add($"{record.Id}_{firstSection}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading notes: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        private Dictionary<string, Dictionary<string, string>>? ParseNotesJson(string? notesJson)
        {
            try
            {
                if (string.IsNullOrEmpty(notesJson))
                    return null;

                return JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(notesJson);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing notes JSON: {ex.Message}");
                return null;
            }
        }

        // New method to process markdown content
        private Dictionary<string, Dictionary<string, MarkupString>>? ProcessMarkdownNotes(
        Dictionary<string, Dictionary<string, string>>? parsedNotes)
        {
            if (parsedNotes == null)
                return null;

            var processedNotes = new Dictionary<string, Dictionary<string, MarkupString>>();

            foreach (var section in parsedNotes)
            {
                var processedSection = new Dictionary<string, MarkupString>();

                foreach (var field in section.Value)
                {
                    if (!string.IsNullOrEmpty(field.Value))
                    {
                        // Clean the content before processing with Markdig
                        var cleanedContent = field.Value
                            .Replace("Manual Content", "", StringComparison.OrdinalIgnoreCase)
                            .Trim();

                        if (!string.IsNullOrEmpty(cleanedContent))
                        {
                            var htmlContent = Markdown.ToHtml(cleanedContent, Pipeline);
                            processedSection[field.Key] = new MarkupString(htmlContent);
                        }
                    }
                }

                if (processedSection.Any())
                {
                    processedNotes[section.Key] = processedSection;
                }
            }

            return processedNotes;
        }


        // Helper method to process individual markdown strings
        private MarkupString ConvertMarkdownToHtml(string markdownText)
        {
            if (string.IsNullOrEmpty(markdownText))
                return new MarkupString("");

            var htmlContent = Markdown.ToHtml(markdownText, Pipeline);
            return new MarkupString(htmlContent);
        }

        private void ToggleSection(Guid recordId, string sectionKey)
        {
            var key = $"{recordId}_{sectionKey}";

            if (expandedSections.Contains(key))
            {
                expandedSections.Remove(key);
            }
            else
            {
                expandedSections.Add(key);
            }
        }

        public void Dispose()
        {
            _disposed = true;
        }
    }
}
